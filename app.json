{"expo": {"name": "middlecrop", "slug": "middlecrop", "version": "1.0.0", "scheme": "middlecrop", "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-dev-launcher", {"launchMode": "most-recent"}], "expo-web-browser"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.ad78.middlecrop"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.ad78.middlecrop"}, "extra": {"router": {}, "eas": {"projectId": "39c738d3-492e-463c-a522-1a69845ac40e"}}}}